<template>
  <div style="height: 100vh; padding: 20px 16px">
    <div style="color: #e14c46">
      *<span style="color: #272935">添加描述:</span>
    </div>
    <Field
      v-model="deliveryDesc"
      style="
        height: 95px;
        margin: 10px 0;
        border-radius: 5px;
        border: 1px solid #e8e8e8;
      "
      type="textarea"
      maxlength="50"
      placeholder="请输入任务完成情况"
      show-word-limit
    />
    <div style="color: #e14c46">
      *<span style="color: #272935">上传成果:</span>
    </div>
    <MobileUploader
      style="margin: 10px 0"
      v-model="uploadedFiles"
      accept="image/*,video/*,.pdf,.doc,.docx,.xls,.xlsx,.zip,.rar"
      :max-size="20"
      placeholder="点击上传图片、视频或文件"
      @upload-success="handleUploadSuccess"
      @upload-error="handleUploadError"
      @file-selecting-start="handleFileSelectingStart"
      @file-selecting-end="handleFileSelectingEnd"
    />
    <div style="color: #8d93a6">
      格式支持：jpg，jpeg，png，pdf，xls，xlsx，MP4，AVI，WMV，RMVB，mov，zip，rar
    </div>
    <div style="color: #8d93a6">支持大小20M</div>
    <div
      style="
        width: 100vw;
        height: 40px;
        position: fixed;
        bottom: 0;
        padding: 20px 0;
      "
    >
      <Button class="btn" @click="handleConfirm" :loading="loading"
        >确 认</Button
      >
    </div>
  </div>
</template>
<script>
import { Field, Button } from 'vant'
import MobileUploader from '../../../components/MobileUploader.vue'
import handleError from '../../../helpers/handleErrorH5'
import makeClient from '../../../services/operateLabor/makeClient'
const client = makeClient()

export default {
  components: {
    Field,
    Button,
    MobileUploader
  },
  data() {
    return {
      deliveryDesc: '',
      uploadedFiles: [],
      loading: false,
      isFileSelecting: false // 标记是否正在选择文件
    }
  },
  created() {
    this.setupVisibilityProtection()
    // 添加全局标记，防止其他组件的认证检查
    window.__isInDeliveryPage = true
    console.log('进入delivery页面，设置全局标记')
  },
  beforeDestroy() {
    this.cleanupVisibilityProtection()
    // 移除全局标记
    window.__isInDeliveryPage = false
    window.__fileSelectingGlobal = false
    console.log('离开delivery页面，清除全局标记')
  },
  methods: {
    setupVisibilityProtection() {
      // 在Android设备上防止文件上传时的页面跳转问题
      const isAndroid = /Android/i.test(navigator.userAgent)
      if (isAndroid) {
        this.visibilityChangeHandler = () => {
          if (document.hidden && this.isFileSelecting) {
            // 正在选择文件时页面失去焦点，忽略此次可见性变化
            console.log('正在选择文件，忽略页面可见性变化')
            return
          }
          
          if (!document.hidden && !this.isFileSelecting) {
            // 页面重新可见且不是文件选择引起的
            console.log('页面重新可见，检查是否需要重新认证')
            // 这里可以添加重新认证的逻辑，但对于交付页面可能不需要
          }
        }
        
        document.addEventListener('visibilitychange', this.visibilityChangeHandler)
      }
    },
    
    cleanupVisibilityProtection() {
      if (this.visibilityChangeHandler) {
        document.removeEventListener('visibilitychange', this.visibilityChangeHandler)
      }
    },
    
    // 文件选择开始
    handleFileSelectingStart() {
      this.isFileSelecting = true
      // 设置全局标记，防止其他页面的可见性监听器干扰
      window.__fileSelectingGlobal = true
      console.log('开始选择文件')
    },
    
    // 文件选择结束
    handleFileSelectingEnd() {
      // 延迟标记结束，避免时序问题
      setTimeout(() => {
        this.isFileSelecting = false
        // 清除全局标记
        window.__fileSelectingGlobal = false
        console.log('结束选择文件')
      }, 3000) // 延长到3秒，确保绝对安全
    },
    
    // 处理上传成功
    handleUploadSuccess(fileData) {
      console.log('上传成功:', fileData)
    },

    // 处理上传错误
    handleUploadError(error) {
      console.error('上传失败:', error)
    },

    async handleConfirm() {
      if (!this.deliveryDesc) {
        this.$toast('请添加描述')
        return
      }
      if (!this.uploadedFiles.length) {
        this.$toast('请上传成果')
        return
      }

      this.loading = true

      // 获取文件ID（新的上传组件已经完成了上传）
      const fileId = this.uploadedFiles[0].id

      if (!fileId) {
        this.loading = false
        this.$toast('文件上传失败，请重新上传')
        return
      }

      const [err, response] = await client.personalAddDelivery({
        body: {
          taskId: this.$route.query.taskId,
          fileId: [fileId],
          deliveryDesc: this.deliveryDesc
        }
      })

      this.loading = false

      if (err) {
        return handleError(err)
      }

      if (response.success) {
        this.$toast('交付成功')
        this.$router.go(-1)
      } else {
        this.$toast(response.message || '交付失败')
      }
    }
  }
}
</script>
<style scoped>
.btn {
  width: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #0082fe;
  color: #fff;
  border-radius: 20px;
  padding: 10px;
  margin-left: 16px;
}
</style>
