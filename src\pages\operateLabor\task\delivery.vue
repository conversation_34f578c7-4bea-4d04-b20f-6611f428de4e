<template>
  <div style="height: 100vh; padding: 20px 16px">
    <div style="color: #e14c46">
      *<span style="color: #272935">添加描述:</span>
    </div>
    <Field
      v-model="deliveryDesc"
      style="
        height: 95px;
        margin: 10px 0;
        border-radius: 5px;
        border: 1px solid #e8e8e8;
      "
      type="textarea"
      maxlength="50"
      placeholder="请输入任务完成情况"
      show-word-limit
    />
    <div style="color: #e14c46">
      *<span style="color: #272935">上传成果:</span>
    </div>
    <MobileUploader
      style="margin: 10px 0"
      v-model="uploadedFiles"
      accept="image/*,video/*,.pdf,.doc,.docx,.xls,.xlsx,.zip,.rar"
      :max-size="20"
      placeholder="点击上传图片、视频或文件"
      @upload-success="handleUploadSuccess"
      @upload-error="handleUploadError"
    />
    <div style="color: #8d93a6">
      格式支持：jpg，jpeg，png，pdf，xls，xlsx，MP4，AVI，WMV，RMVB，mov，zip，rar
    </div>
    <div style="color: #8d93a6">支持大小20M</div>
    <div
      style="
        width: 100vw;
        height: 40px;
        position: fixed;
        bottom: 0;
        padding: 20px 0;
      "
    >
      <Button class="btn" @click="handleConfirm" :loading="loading"
        >确 认</Button
      >
    </div>
  </div>
</template>
<script>
import { Field, Button } from 'vant'
import MobileUploader from '../../../components/MobileUploader.vue'
import handleError from '../../../helpers/handleErrorH5'
import makeClient from '../../../services/operateLabor/makeClient'
const client = makeClient()

export default {
  components: {
    Field,
    Button,
    MobileUploader
  },
  data() {
    return {
      deliveryDesc: '',
      uploadedFiles: [],
      loading: false
    }
  },
  methods: {
    // 处理上传成功
    handleUploadSuccess(fileData) {
      console.log('上传成功:', fileData)
    },

    // 处理上传错误
    handleUploadError(error) {
      console.error('上传失败:', error)
    },

    async handleConfirm() {
      if (!this.deliveryDesc) {
        this.$toast('请添加描述')
        return
      }
      if (!this.uploadedFiles.length) {
        this.$toast('请上传成果')
        return
      }

      this.loading = true

      // 获取文件ID（新的上传组件已经完成了上传）
      const fileId = this.uploadedFiles[0].id

      if (!fileId) {
        this.loading = false
        this.$toast('文件上传失败，请重新上传')
        return
      }

      const [err, response] = await client.personalAddDelivery({
        body: {
          taskId: this.$route.query.taskId,
          fileId: [fileId],
          deliveryDesc: this.deliveryDesc
        }
      })

      this.loading = false

      if (err) {
        return handleError(err)
      }

      if (response.success) {
        this.$toast('交付成功')
        this.$router.go(-1)
      } else {
        this.$toast(response.message || '交付失败')
      }
    }
  }
}
</script>
<style scoped>
.btn {
  width: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #0082fe;
  color: #fff;
  border-radius: 20px;
  padding: 10px;
  margin-left: 16px;
}
</style>
