<template>
  <div class="mobile-uploader">
    <div 
      class="upload-area" 
      @click="triggerUpload"
      :class="{ 'uploading': uploading, 'has-file': hasFile }"
    >
      <div v-if="!hasFile" class="upload-placeholder">
        <div class="upload-icon">📁</div>
        <div class="upload-text">{{ placeholder }}</div>
      </div>
      
      <div v-else class="file-preview">
        <div class="file-info">
          <div class="file-name">{{ fileName }}</div>
          <div class="file-size">{{ formatFileSize(fileSize) }}</div>
        </div>
        <div class="file-actions">
          <button @click.stop="removeFile" class="remove-btn">删除</button>
        </div>
      </div>
      
      <div v-if="uploading" class="upload-loading">
        <div class="loading-spinner"></div>
        <div>上传中...</div>
      </div>
    </div>
    
    <!-- 隐藏的文件输入 -->
    <input
      ref="fileInput"
      type="file"
      :accept="accept"
      @change="handleFileChange"
      style="display: none;"
    />
    
    <div v-if="error" class="error-message">{{ error }}</div>
  </div>
</template>

<script>
import makeClient from '../services/operateLabor/makeClient'
const client = makeClient()

export default {
  name: 'MobileUploader',
  props: {
    value: {
      type: Array,
      default: () => []
    },
    accept: {
      type: String,
      default: '*'
    },
    maxSize: {
      type: Number,
      default: 50 // MB
    },
    placeholder: {
      type: String,
      default: '点击上传文件'
    }
  },
  data() {
    return {
      uploading: false,
      error: '',
      currentFile: null,
      preventNavigation: false
    }
  },
  computed: {
    hasFile() {
      return this.value && this.value.length > 0
    },
    fileName() {
      return this.hasFile ? this.value[0].name : ''
    },
    fileSize() {
      return this.hasFile ? this.value[0].size : 0
    }
  },
  mounted() {
    this.setupMobileOptimization()
  },
  beforeDestroy() {
    this.cleanup()
  },
  methods: {
    setupMobileOptimization() {
      // 检测移动设备
      const isMobile = /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
      const isAndroid = /Android/i.test(navigator.userAgent)
      
      if (isMobile) {
        // 防止页面滚动
        document.body.style.touchAction = 'manipulation'
        
        if (isAndroid) {
          // Android特殊处理
          this.setupAndroidProtection()
        }
      }
    },
    
    setupAndroidProtection() {
      // 监听页面可见性变化
      this.visibilityHandler = () => {
        if (document.hidden && this.uploading) {
          // 页面隐藏时如果正在上传，标记防止导航
          this.preventNavigation = true
        }
      }
      
      document.addEventListener('visibilitychange', this.visibilityHandler)
      
      // 监听beforeunload事件
      this.beforeUnloadHandler = (e) => {
        if (this.preventNavigation || this.uploading) {
          e.preventDefault()
          e.returnValue = ''
          return ''
        }
      }
      
      window.addEventListener('beforeunload', this.beforeUnloadHandler)
    },
    
    cleanup() {
      if (this.visibilityHandler) {
        document.removeEventListener('visibilitychange', this.visibilityHandler)
      }
      if (this.beforeUnloadHandler) {
        window.removeEventListener('beforeunload', this.beforeUnloadHandler)
      }
    },
    
    triggerUpload() {
      if (this.uploading) return
      
      this.error = ''
      this.preventNavigation = true
      
      // 延迟触发文件选择，确保事件处理完成
      this.$nextTick(() => {
        this.$refs.fileInput.click()
      })
      
      // 设置超时重置，防止用户取消选择后状态不重置
      setTimeout(() => {
        this.preventNavigation = false
      }, 10000)
    },
    
    async handleFileChange(event) {
      const file = event.target.files[0]
      if (!file) {
        this.preventNavigation = false
        return
      }
      
      // 验证文件
      const validation = this.validateFile(file)
      if (validation) {
        this.error = validation
        this.preventNavigation = false
        event.target.value = '' // 清空input
        return
      }
      
      // 开始上传
      await this.uploadFile(file)
      
      // 清空input以允许重新选择同一文件
      event.target.value = ''
      this.preventNavigation = false
    },
    
    validateFile(file) {
      // 文件大小验证
      if (file.size > this.maxSize * 1024 * 1024) {
        return `文件大小不能超过 ${this.maxSize}MB`
      }
      
      // 文件类型验证（如果指定了accept）
      if (this.accept !== '*') {
        const acceptTypes = this.accept.split(',').map(type => type.trim())
        const fileType = file.type
        const fileName = file.name.toLowerCase()
        
        let isValidType = false
        
        for (let acceptType of acceptTypes) {
          if (acceptType.startsWith('.')) {
            // 扩展名匹配
            if (fileName.endsWith(acceptType.toLowerCase())) {
              isValidType = true
              break
            }
          } else if (acceptType.includes('/*')) {
            // MIME类型匹配
            const baseType = acceptType.split('/')[0]
            if (fileType.startsWith(baseType)) {
              isValidType = true
              break
            }
          } else if (fileType === acceptType) {
            // 精确MIME类型匹配
            isValidType = true
            break
          }
        }
        
        if (!isValidType) {
          return '文件格式不支持'
        }
      }
      
      return null
    },
    
    async uploadFile(file) {
      this.uploading = true
      this.error = ''
      
      try {
        const formData = new FormData()
        formData.append('file', file)
        
        const [err, response] = await client.uploadFile({
          body: formData
        })
        
        if (err) {
          throw new Error(err.message || '上传失败')
        }
        
        if (response.success) {
          const fileData = {
            file: file,
            name: file.name,
            size: file.size,
            id: response.data.fileId,
            url: this.getFileUrl(response.data.fileId)
          }
          
          this.$emit('input', [fileData])
          this.$emit('upload-success', fileData)
          this.$toast('上传成功')
        } else {
          throw new Error(response.message || '上传失败')
        }
      } catch (error) {
        this.error = error.message
        this.$emit('upload-error', error)
        this.$toast(error.message)
      } finally {
        this.uploading = false
      }
    },
    
    removeFile() {
      this.$emit('input', [])
      this.$emit('file-remove')
      this.currentFile = null
      this.error = ''
    },
    
    getFileUrl(fileId) {
      return `${window.env?.apiPath}/api/public/downloadFile/${fileId}`
    },
    
    formatFileSize(bytes) {
      if (bytes === 0) return '0 B'
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    }
  }
}
</script>

<style scoped>
.mobile-uploader {
  width: 100%;
}

.upload-area {
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #fafafa;
  position: relative;
  min-height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.upload-area:hover {
  border-color: #1890ff;
  background: #f0f8ff;
}

.upload-area.uploading {
  border-color: #1890ff;
  background: #f0f8ff;
  cursor: not-allowed;
}

.upload-area.has-file {
  border-color: #52c41a;
  background: #f6ffed;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.upload-icon {
  font-size: 32px;
  opacity: 0.6;
}

.upload-text {
  color: #666;
  font-size: 14px;
}

.file-preview {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.file-info {
  flex: 1;
  text-align: left;
}

.file-name {
  font-size: 14px;
  color: #333;
  font-weight: 500;
  margin-bottom: 4px;
}

.file-size {
  font-size: 12px;
  color: #999;
}

.file-actions {
  display: flex;
  gap: 8px;
}

.remove-btn {
  background: #ff4d4f;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 12px;
  cursor: pointer;
}

.upload-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  color: #1890ff;
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #1890ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-message {
  color: #ff4d4f;
  font-size: 12px;
  margin-top: 8px;
  text-align: center;
}

/* 移动端优化 */
@media (max-width: 768px) {
  .upload-area {
    padding: 16px;
    min-height: 60px;
  }
  
  .upload-icon {
    font-size: 24px;
  }
  
  .upload-text {
    font-size: 13px;
  }
  
  .file-name {
    font-size: 13px;
  }
  
  .remove-btn {
    padding: 6px 10px;
    font-size: 13px;
  }
}
</style>
