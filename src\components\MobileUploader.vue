<template>
  <div class="mobile-uploader">
    <div
      class="upload-area"
      @click="triggerUpload"
      :class="{ 'uploading': uploading, 'has-file': hasFile }"
    >
      <!-- 上传中状态 -->
      <div v-if="uploading" class="upload-loading">
        <div class="loading-spinner"></div>
        <div class="loading-text">上传中...</div>
      </div>

      <!-- 未上传状态 -->
      <div v-else-if="!hasFile" class="upload-placeholder">
        <div class="upload-icon">📁</div>
        <div class="upload-text">{{ placeholder }}</div>
        <div class="upload-hint">支持图片、视频、文档等多种格式</div>
      </div>

      <!-- 已上传文件预览 -->
      <div v-else class="file-preview">
        <!-- 图片预览 -->
        <div v-if="isImage" class="image-preview" @click.stop="previewImage">
          <img :src="previewUrl" :alt="fileName" />
          <div class="preview-overlay">
            <span>点击预览</span>
          </div>
        </div>

        <!-- 视频预览 -->
        <div v-else-if="isVideo" class="video-preview">
          <video
            ref="videoPlayer"
            :src="previewUrl"
            controls
            preload="metadata"
            @click.stop
            playsinline
            webkit-playsinline
          >
            您的浏览器不支持视频播放
          </video>
          <div class="video-info">
            <div class="file-name">{{ fileName }}</div>
            <div class="file-size">{{ formatFileSize(fileSize) }}</div>
          </div>
        </div>

        <!-- 其他文件预览 -->
        <div v-else class="file-info-preview">
          <div class="file-icon">{{ getFileIcon() }}</div>
          <div class="file-details">
            <div class="file-name">{{ fileName }}</div>
            <div class="file-size">{{ formatFileSize(fileSize) }}</div>
            <div class="file-type">{{ getFileType() }}</div>
          </div>
        </div>

        <div class="file-actions">
          <button @click.stop="downloadFile" class="download-btn">下载</button>
          <button @click.stop="removeFile" class="remove-btn">删除</button>
        </div>
      </div>
    </div>

    <!-- 隐藏的文件输入 -->
    <input
      ref="fileInput"
      type="file"
      :accept="acceptAttribute"
      @change="handleFileChange"
      style="display: none;"
    />

    <div v-if="error" class="error-message">{{ error }}</div>

    <!-- 图片预览弹窗 -->
    <div v-if="showImagePreview" class="image-preview-modal" @click="closeImagePreview">
      <div class="modal-content" @click.stop>
        <img :src="previewUrl" :alt="fileName" />
        <button class="close-btn" @click="closeImagePreview">×</button>
      </div>
    </div>
  </div>
</template>

<script>
import makeClient from '../services/operateLabor/makeClient'
const client = makeClient()

export default {
  name: 'MobileUploader',
  props: {
    value: {
      type: Array,
      default: () => []
    },
    accept: {
      type: String,
      default: '*'
    },
    maxSize: {
      type: Number,
      default: 50 // MB
    },
    placeholder: {
      type: String,
      default: '点击上传文件'
    }
  },
  data() {
    return {
      uploading: false,
      error: '',
      currentFile: null,
      preventNavigation: false,
      showImagePreview: false
    }
  },
  computed: {
    hasFile() {
      return this.value && this.value.length > 0
    },
    fileName() {
      return this.hasFile ? this.value[0].name : ''
    },
    fileSize() {
      return this.hasFile ? this.value[0].size : 0
    },
    previewUrl() {
      return this.hasFile ? this.value[0].url : ''
    },
    isImage() {
      if (!this.hasFile) return false
      const fileName = this.fileName.toLowerCase()
      return /\.(jpg|jpeg|png|gif|bmp|webp)$/.test(fileName)
    },
    isVideo() {
      if (!this.hasFile) return false
      const fileName = this.fileName.toLowerCase()
      return /\.(mp4|avi|mov|wmv|rmvb|flv|mkv|webm)$/.test(fileName)
    },
    acceptAttribute() {
      // 转换accept属性为标准格式
      if (this.accept === '*') {
        return '*/*'
      }
      return this.accept
    }
  },
  mounted() {
    this.setupMobileOptimization()
  },
  beforeDestroy() {
    this.cleanup()
  },
  methods: {
    setupMobileOptimization() {
      // 检测移动设备
      const isMobile = /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
      const isAndroid = /Android/i.test(navigator.userAgent)

      if (isMobile) {
        // 防止页面滚动
        document.body.style.touchAction = 'manipulation'

        if (isAndroid) {
          // Android特殊处理
          this.setupAndroidProtection()
        }
      }
    },

    setupAndroidProtection() {
      // 监听页面可见性变化
      this.visibilityHandler = () => {
        if (document.hidden && this.uploading) {
          // 页面隐藏时如果正在上传，标记防止导航
          this.preventNavigation = true
        }
      }

      document.addEventListener('visibilitychange', this.visibilityHandler)

      // 监听beforeunload事件
      this.beforeUnloadHandler = (e) => {
        if (this.preventNavigation || this.uploading) {
          e.preventDefault()
          e.returnValue = ''
          return ''
        }
      }

      window.addEventListener('beforeunload', this.beforeUnloadHandler)
    },

    cleanup() {
      if (this.visibilityHandler) {
        document.removeEventListener('visibilitychange', this.visibilityHandler)
      }
      if (this.beforeUnloadHandler) {
        window.removeEventListener('beforeunload', this.beforeUnloadHandler)
      }
    },

    // 获取文件图标
    getFileIcon() {
      const fileName = this.fileName.toLowerCase()
      if (/\.(pdf)$/.test(fileName)) return '📄'
      if (/\.(doc|docx)$/.test(fileName)) return '📝'
      if (/\.(xls|xlsx)$/.test(fileName)) return '📊'
      if (/\.(zip|rar|7z)$/.test(fileName)) return '🗜️'
      if (/\.(txt)$/.test(fileName)) return '📃'
      return '📁'
    },

    // 获取文件类型描述
    getFileType() {
      const fileName = this.fileName.toLowerCase()
      if (/\.(jpg|jpeg|png|gif|bmp|webp)$/.test(fileName)) return '图片文件'
      if (/\.(mp4|avi|mov|wmv|rmvb|flv|mkv|webm)$/.test(fileName)) return '视频文件'
      if (/\.(pdf)$/.test(fileName)) return 'PDF文档'
      if (/\.(doc|docx)$/.test(fileName)) return 'Word文档'
      if (/\.(xls|xlsx)$/.test(fileName)) return 'Excel表格'
      if (/\.(zip|rar|7z)$/.test(fileName)) return '压缩文件'
      if (/\.(txt)$/.test(fileName)) return '文本文件'
      return '文件'
    },

    // 预览图片
    previewImage() {
      this.showImagePreview = true
    },

    // 关闭图片预览
    closeImagePreview() {
      this.showImagePreview = false
    },



    // 下载文件
    downloadFile() {
      if (this.hasFile && this.value[0].url) {
        window.open(this.value[0].url, '_blank')
      }
    },
    
    triggerUpload() {
      if (this.uploading || this.hasFile) return

      this.error = ''
      this.preventNavigation = true

      // 延迟触发文件选择，确保事件处理完成
      this.$nextTick(() => {
        this.$refs.fileInput.click()
      })

      // 设置超时重置，防止用户取消选择后状态不重置
      setTimeout(() => {
        this.preventNavigation = false
      }, 10000)
    },
    
    async handleFileChange(event) {
      const file = event.target.files[0]
      if (!file) {
        this.preventNavigation = false
        return
      }
      
      // 验证文件
      const validation = this.validateFile(file)
      if (validation) {
        this.error = validation
        this.preventNavigation = false
        event.target.value = '' // 清空input
        return
      }
      
      // 开始上传
      await this.uploadFile(file)
      
      // 清空input以允许重新选择同一文件
      event.target.value = ''
      this.preventNavigation = false
    },
    
    validateFile(file) {
      // 文件大小验证
      if (file.size > this.maxSize * 1024 * 1024) {
        return `文件大小不能超过 ${this.maxSize}MB`
      }
      
      // 文件类型验证（如果指定了accept）
      if (this.accept !== '*') {
        const acceptTypes = this.accept.split(',').map(type => type.trim())
        const fileType = file.type
        const fileName = file.name.toLowerCase()
        
        let isValidType = false
        
        for (let acceptType of acceptTypes) {
          if (acceptType.startsWith('.')) {
            // 扩展名匹配
            if (fileName.endsWith(acceptType.toLowerCase())) {
              isValidType = true
              break
            }
          } else if (acceptType.includes('/*')) {
            // MIME类型匹配
            const baseType = acceptType.split('/')[0]
            if (fileType.startsWith(baseType)) {
              isValidType = true
              break
            }
          } else if (fileType === acceptType) {
            // 精确MIME类型匹配
            isValidType = true
            break
          }
        }
        
        if (!isValidType) {
          return '文件格式不支持'
        }
      }
      
      return null
    },
    
    async uploadFile(file) {
      this.uploading = true
      this.error = ''
      
      try {
        const formData = new FormData()
        formData.append('file', file)
        
        const [err, response] = await client.uploadFile({
          body: formData
        })
        
        if (err) {
          throw new Error(err.message || '上传失败')
        }
        
        if (response.success) {
          const fileData = {
            file: file,
            name: file.name,
            size: file.size,
            id: response.data.fileId,
            url: this.getFileUrl(response.data.fileId)
          }
          
          this.$emit('input', [fileData])
          this.$emit('upload-success', fileData)
          this.$toast('上传成功')
        } else {
          throw new Error(response.message || '上传失败')
        }
      } catch (error) {
        this.error = error.message
        this.$emit('upload-error', error)
        this.$toast(error.message)
      } finally {
        this.uploading = false
      }
    },
    
    removeFile() {
      this.$emit('input', [])
      this.$emit('file-remove')
      this.currentFile = null
      this.error = ''
    },
    
    getFileUrl(fileId) {
      return `${window.env?.apiPath}/api/public/downloadFile/${fileId}`
    },
    
    formatFileSize(bytes) {
      if (bytes === 0) return '0 B'
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    }
  }
}
</script>

<style scoped>
.mobile-uploader {
  width: 100%;
}

.upload-area {
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #fafafa;
  position: relative;
  min-height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.upload-area:hover {
  border-color: #1890ff;
  background: #f0f8ff;
}

.upload-area.uploading {
  border-color: #1890ff;
  background: #f0f8ff;
  cursor: not-allowed;
  pointer-events: none;
}

.upload-area.has-file {
  border-color: #52c41a;
  background: #f6ffed;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.upload-icon {
  font-size: 32px;
  opacity: 0.6;
}

.upload-text {
  color: #666;
  font-size: 14px;
  margin-bottom: 4px;
}

.upload-hint {
  color: #999;
  font-size: 12px;
}

.file-preview {
  display: flex;
  flex-direction: column;
  width: 100%;
  gap: 12px;
}

/* 图片预览样式 */
.image-preview {
  position: relative;
  width: 100%;
  max-width: 200px;
  margin: 0 auto;
  cursor: pointer;
  border-radius: 8px;
  overflow: hidden;
}

.image-preview img {
  width: 100%;
  height: auto;
  max-height: 150px;
  object-fit: cover;
  display: block;
}

.preview-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.image-preview:hover .preview-overlay {
  opacity: 1;
}

/* 视频预览样式 */
.video-preview {
  width: 100%;
  max-width: 300px;
  margin: 0 auto;
  border-radius: 8px;
  overflow: hidden;
  background: #000;
}

.video-preview video {
  width: 100%;
  height: auto;
  max-height: 200px;
  display: block;
  border-radius: 8px;
}

.video-info {
  padding: 8px 12px;
  background: #f8f9fa;
  text-align: left;
}

.video-info .file-name {
  font-size: 13px;
  color: #333;
  font-weight: 500;
  margin-bottom: 4px;
  word-break: break-all;
}

.video-info .file-size {
  font-size: 12px;
  color: #999;
}

/* 文件信息预览样式 */
.file-info-preview {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 8px;
}

.file-icon {
  font-size: 32px;
  flex-shrink: 0;
}

.file-details {
  flex: 1;
  text-align: left;
}

.file-name {
  font-size: 14px;
  color: #333;
  font-weight: 500;
  margin-bottom: 4px;
  word-break: break-all;
}

.file-size {
  font-size: 12px;
  color: #999;
  margin-bottom: 2px;
}

.file-type {
  font-size: 12px;
  color: #666;
}

.file-actions {
  display: flex;
  gap: 8px;
  justify-content: center;
  margin-top: 8px;
}

.download-btn {
  background: #1890ff;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 12px;
  cursor: pointer;
  transition: background 0.3s ease;
}

.download-btn:hover {
  background: #40a9ff;
}

.remove-btn {
  background: #ff4d4f;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 12px;
  cursor: pointer;
  transition: background 0.3s ease;
}

.remove-btn:hover {
  background: #ff7875;
}

/* 图片预览弹窗样式 */
.image-preview-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  padding: 20px;
}

.modal-content {
  position: relative;
  max-width: 90vw;
  max-height: 90vh;
  background: white;
  border-radius: 8px;
  overflow: hidden;
}

.modal-content img {
  width: 100%;
  height: auto;
  max-width: 100%;
  max-height: 80vh;
  object-fit: contain;
  display: block;
}

.close-btn {
  position: absolute;
  top: 10px;
  right: 10px;
  background: rgba(0, 0, 0, 0.5);
  color: white;
  border: none;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  font-size: 18px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.upload-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 12px;
  background: rgba(255, 255, 255, 0.9);
  color: #1890ff;
  z-index: 10;
}

.loading-text {
  font-size: 14px;
  font-weight: 500;
}

.loading-spinner {
  width: 24px;
  height: 24px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #1890ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-message {
  color: #ff4d4f;
  font-size: 12px;
  margin-top: 8px;
  text-align: center;
}

/* 移动端优化 */
@media (max-width: 768px) {
  .upload-area {
    padding: 16px;
    min-height: 60px;
  }
  
  .upload-icon {
    font-size: 24px;
  }
  
  .upload-text {
    font-size: 13px;
  }
  
  .file-name {
    font-size: 13px;
  }
  
  .remove-btn {
    padding: 6px 10px;
    font-size: 13px;
  }
}
</style>
