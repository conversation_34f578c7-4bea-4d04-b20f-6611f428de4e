<template>
  <div class="mobile-uploader">
    <div
      class="upload-area"
      @click="triggerUpload"
      :class="{ 'uploading': uploading, 'has-file': hasFile }"
    >
      <!-- 上传中状态 -->
      <div v-if="uploading" class="upload-loading">
        <div class="loading-spinner"></div>
        <div class="loading-text">上传中...</div>
      </div>

      <!-- 未上传状态 -->
      <div v-else-if="!hasFile" class="upload-placeholder">
        <div class="upload-icon">📁</div>
        <div class="upload-text">{{ placeholder }}</div>
        <div class="upload-hint">支持图片、视频、文档等多种格式</div>
      </div>

      <!-- 已上传文件预览 -->
      <div v-else class="file-preview">
        <!-- 图片预览 -->
        <div v-if="isImage" class="image-preview" @click.stop="previewImage">
          <img :src="previewUrl" :alt="fileName" />
          <div class="preview-overlay">
            <span>点击预览</span>
          </div>
        </div>

        <!-- 视频预览 -->
        <div v-else-if="isVideo" class="video-preview">
          <video
            ref="videoPlayer"
            :src="previewUrl"
            controls
            preload="metadata"
            @click.stop
            playsinline
            webkit-playsinline
            x-webkit-airplay="allow"
            x5-video-player-type="h5"
            x5-video-orientation="portraint"
            x5-playsinline
            muted
            @error="handleVideoError"
            @loadstart="handleVideoLoadStart"
            @canplay="handleVideoCanPlay"
            @loadedmetadata="handleVideoLoadedMetadata"
          >
            您的浏览器不支持视频播放
          </video>
          <div class="video-info">
            <div class="file-name">{{ fileName }}</div>
            <div class="file-size">{{ formatFileSize(fileSize) }}</div>
            <button @click.stop="playVideo" class="play-btn">▶ 播放视频</button>
          </div>
        </div>

        <!-- 其他文件预览 -->
        <div v-else class="file-info-preview">
          <div class="file-icon">{{ getFileIcon() }}</div>
          <div class="file-details">
            <div class="file-name">{{ fileName }}</div>
            <div class="file-size">{{ formatFileSize(fileSize) }}</div>
            <div class="file-type">{{ getFileType() }}</div>
          </div>
        </div>

        <div class="file-actions">
          <!-- <button @click.stop="downloadFile" class="download-btn">下载</button> -->
          <button @click.stop="removeFile" class="remove-btn">删除</button>
        </div>
      </div>
    </div>

    <!-- 隐藏的文件输入 -->
    <input
      ref="fileInput"
      type="file"
      :accept="acceptAttribute"
      @change="handleFileChange"
      style="display: none;"
    />

    <div v-if="error" class="error-message">{{ error }}</div>

    <!-- 图片预览弹窗 -->
    <div v-if="showImagePreview" class="image-preview-modal" @click="closeImagePreview">
      <div class="modal-content" @click.stop>
        <img :src="previewUrl" :alt="fileName" />
        <button class="close-btn" @click="closeImagePreview">×</button>
      </div>
    </div>
  </div>
</template>

<script>
import makeClient from '../services/operateLabor/makeClient'
const client = makeClient()

export default {
  name: 'MobileUploader',
  props: {
    value: {
      type: Array,
      default: () => []
    },
    accept: {
      type: String,
      default: '*'
    },
    maxSize: {
      type: Number,
      default: 50 // MB
    },
    placeholder: {
      type: String,
      default: '点击上传文件'
    }
  },
  data() {
    return {
      uploading: false,
      error: '',
      currentFile: null,
      showImagePreview: false,
      isFileSelecting: false
    }
  },
  computed: {
    hasFile() {
      return this.value && this.value.length > 0
    },
    fileName() {
      return this.hasFile ? this.value[0].name : ''
    },
    fileSize() {
      return this.hasFile ? this.value[0].size : 0
    },
    previewUrl() {
      return this.hasFile ? this.value[0].url : ''
    },
    isImage() {
      if (!this.hasFile) return false
      const fileName = this.fileName.toLowerCase()
      return /\.(jpg|jpeg|png|gif|bmp|webp)$/.test(fileName)
    },
    isVideo() {
      if (!this.hasFile) return false
      const fileName = this.fileName.toLowerCase()
      return /\.(mp4|avi|mov|wmv|rmvb|flv|mkv|webm)$/.test(fileName)
    },
    acceptAttribute() {
      // 转换accept属性为标准格式
      if (this.accept === '*') {
        return '*/*'
      }
      return this.accept
    }
  },
  mounted() {
    this.setupMobileOptimization()
  },
  beforeDestroy() {
    this.cleanup()
  },
  methods: {
    setupMobileOptimization() {
      // 检测移动设备
      const isMobile = /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
      const isAndroid = /Android/i.test(navigator.userAgent)

      if (isMobile) {
        // 防止页面滚动
        document.body.style.touchAction = 'manipulation'

        if (isAndroid) {
          // Android特殊处理
          this.setupAndroidProtection()
        }
      }
    },

    setupAndroidProtection() {
      // Android设备防护，阻止意外跳转
      const isAndroid = /Android/i.test(navigator.userAgent)
      if (isAndroid) {
        // 推入一个状态，防止后退
        history.pushState(null, null, location.href)

        // 阻止所有可能的跳转
        window.addEventListener('beforeunload', (e) => {
          e.preventDefault()
          return false
        }, true)

        // 阻止 popstate 事件（浏览器后退）
        window.addEventListener('popstate', (e) => {
          e.preventDefault()
          e.stopPropagation()
          e.stopImmediatePropagation()
          // 立即推回当前状态
          history.pushState(null, null, location.href)
          return false
        }, true)

        // 阻止 hashchange 事件
        window.addEventListener('hashchange', (e) => {
          e.preventDefault()
          e.stopPropagation()
          return false
        }, true)

        this.$nextTick(() => {
          const fileInputs = this.$el.querySelectorAll('input[type="file"]')
          fileInputs.forEach(input => {
            // 防止文件选择相关的所有事件冒泡
            input.addEventListener('change', (e) => {
              e.stopPropagation()
              e.stopImmediatePropagation()
            }, true)

            input.addEventListener('focus', (e) => {
              e.stopPropagation()
              e.stopImmediatePropagation()
            }, true)

            input.addEventListener('blur', (e) => {
              e.stopPropagation()
              e.stopImmediatePropagation()
            }, true)

            input.addEventListener('click', (e) => {
              e.stopPropagation()
              e.stopImmediatePropagation()
            }, true)
          })
        })
      }
    },

    cleanup() {
      // 清理方法保留，但现在没有需要清理的监听器
      document.removeEventListener('visibilitychange', this.preventVisibilityChange, { capture: true })
    },

    // 防止文件选择时的可见性变化处理
    preventVisibilityChange(event) {
      if (this.isFileSelecting || window.__fileSelectingGlobal) {
        console.log('正在选择文件，阻止可见性变化处理')
        event.stopImmediatePropagation()
        event.preventDefault()
        return false
      }
    },

    // 获取文件图标
    getFileIcon() {
      const fileName = this.fileName.toLowerCase()
      if (/\.(pdf)$/.test(fileName)) return '📄'
      if (/\.(doc|docx)$/.test(fileName)) return '📝'
      if (/\.(xls|xlsx)$/.test(fileName)) return '📊'
      if (/\.(zip|rar|7z)$/.test(fileName)) return '🗜️'
      if (/\.(txt)$/.test(fileName)) return '📃'
      return '📁'
    },

    // 获取文件类型描述
    getFileType() {
      const fileName = this.fileName.toLowerCase()
      if (/\.(jpg|jpeg|png|gif|bmp|webp)$/.test(fileName)) return '图片文件'
      if (/\.(mp4|avi|mov|wmv|rmvb|flv|mkv|webm)$/.test(fileName)) return '视频文件'
      if (/\.(pdf)$/.test(fileName)) return 'PDF文档'
      if (/\.(doc|docx)$/.test(fileName)) return 'Word文档'
      if (/\.(xls|xlsx)$/.test(fileName)) return 'Excel表格'
      if (/\.(zip|rar|7z)$/.test(fileName)) return '压缩文件'
      if (/\.(txt)$/.test(fileName)) return '文本文件'
      return '文件'
    },

    // 预览图片
    previewImage() {
      this.showImagePreview = true
    },

    // 关闭图片预览
    closeImagePreview() {
      this.showImagePreview = false
    },



    // 播放视频
    playVideo() {
      if (this.$refs.videoPlayer) {
        const video = this.$refs.videoPlayer
        
        // iOS Safari 兼容性处理
        const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent)
        const isSafari = /Safari/.test(navigator.userAgent) && !/Chrome/.test(navigator.userAgent)
        
        if (video.paused) {
          // iOS需要用户手势触发播放
          if (isIOS) {
            // 设置视频属性以提高兼容性
            video.setAttribute('playsinline', 'true')
            video.setAttribute('webkit-playsinline', 'true')
            video.setAttribute('x5-playsinline', 'true')
            video.muted = true // iOS Safari 需要静音才能自动播放
            
            // 尝试全屏播放以避免iOS的播放限制
            if (video.webkitEnterFullscreen) {
              video.webkitEnterFullscreen()
            }
          }
          
          // 尝试播放
          const playPromise = video.play()
          
          if (playPromise !== undefined) {
            playPromise.catch(error => {
              console.error('视频播放失败:', error)
              // 根据错误类型给出不同提示
              if (error.name === 'NotAllowedError') {
                this.$toast('请点击视频区域开始播放')
              } else if (error.name === 'NotSupportedError') {
                this.$toast('视频格式不支持，建议使用MP4格式')
              } else if (error.name === 'AbortError') {
                this.$toast('视频播放被中断')
              } else {
                this.$toast('视频播放失败，请检查网络连接')
              }
            })
          }
        } else {
          video.pause()
        }
      }
    },

    // 视频加载开始
    handleVideoLoadStart() {
      console.log('视频开始加载')
    },

    // 视频可以播放
    handleVideoCanPlay() {
      console.log('视频可以播放')
      // iOS Safari 自动播放策略处理
      const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent)
      if (isIOS && this.$refs.videoPlayer) {
        // 预加载第一帧
        this.$refs.videoPlayer.currentTime = 0.1
      }
    },

    // 视频元数据加载完成
    handleVideoLoadedMetadata() {
      console.log('视频元数据加载完成')
    },

    // 视频加载错误
    handleVideoError(error) {
      console.error('视频加载错误:', error)
      const video = this.$refs.videoPlayer
      if (video && video.error) {
        const errorCode = video.error.code
        let errorMessage = '视频加载失败'
        
        switch (errorCode) {
          case 1:
            errorMessage = '视频加载被中断'
            break
          case 2:
            errorMessage = '网络错误，请检查网络连接'
            break
          case 3:
            errorMessage = '视频解码错误'
            break
          case 4:
            errorMessage = '视频格式不支持'
            break
        }
        
        this.$toast(errorMessage)
      }
    },

    // 下载文件
    downloadFile() {
      if (this.hasFile && this.value[0].url) {
        window.open(this.value[0].url, '_blank')
      }
    },
    
    triggerUpload() {
      if (this.uploading || this.hasFile) return

      this.error = ''

      // Android 特殊处理：在文件选择前加强防护
      const isAndroid = /Android/i.test(navigator.userAgent)
      if (isAndroid) {
        // 推入多个状态，增强防护
        history.pushState(null, null, location.href)
        history.pushState(null, null, location.href)
        history.pushState(null, null, location.href)
      }

      // 直接触发文件选择
      this.$nextTick(() => {
        if (this.$refs.fileInput) {
          this.$refs.fileInput.click()
        }
      })
    },
    
    async handleFileChange(event) {
      // 强制阻止所有事件传播
      event.stopPropagation()
      event.stopImmediatePropagation()
      event.preventDefault()

      const file = event.target.files[0]

      // 如果用户取消了文件选择（没有选择文件）
      if (!file) {
        // Android设备：推回状态确保停留在当前页面
        const isAndroid = /Android/i.test(navigator.userAgent)
        if (isAndroid) {
          history.pushState(null, null, location.href)
        }
        return false
      }

      // 验证文件
      const validation = this.validateFile(file)
      if (validation) {
        this.error = validation
        event.target.value = '' // 清空input
        return
      }

      // 开始上传
      await this.uploadFile(file)

      // 清空input以允许重新选择同一文件
      event.target.value = ''
    },
    
    validateFile(file) {
      // 文件大小验证
      if (file.size > this.maxSize * 1024 * 1024) {
        return `文件大小不能超过 ${this.maxSize}MB`
      }
      
      // 文件类型验证（如果指定了accept）
      if (this.accept !== '*') {
        const acceptTypes = this.accept.split(',').map(type => type.trim())
        const fileType = file.type
        const fileName = file.name.toLowerCase()
        
        let isValidType = false
        
        for (let acceptType of acceptTypes) {
          if (acceptType.startsWith('.')) {
            // 扩展名匹配
            if (fileName.endsWith(acceptType.toLowerCase())) {
              isValidType = true
              break
            }
          } else if (acceptType.includes('/*')) {
            // MIME类型匹配
            const baseType = acceptType.split('/')[0]
            if (fileType.startsWith(baseType)) {
              isValidType = true
              break
            }
          } else if (fileType === acceptType) {
            // 精确MIME类型匹配
            isValidType = true
            break
          }
        }
        
        if (!isValidType) {
          return '文件格式不支持'
        }
      }
      
      return null
    },
    
    async uploadFile(file) {
      this.uploading = true
      this.error = ''
      
      try {
        const formData = new FormData()
        formData.append('file', file)
        
        const [err, response] = await client.uploadFile({
          body: formData
        })
        
        if (err) {
          throw new Error(err.message || '上传失败')
        }
        
        if (response.success) {
          const fileData = {
            file: file,
            name: file.name,
            size: file.size,
            id: response.data.fileId,
            url: this.getFileUrl(response.data.fileId)
          }
          
          this.$emit('input', [fileData])
          this.$emit('upload-success', fileData)
          this.$toast('上传成功')
        } else {
          throw new Error(response.message || '上传失败')
        }
      } catch (error) {
        this.error = error.message
        this.$emit('upload-error', error)
        this.$toast(error.message)
      } finally {
        this.uploading = false
      }
    },
    
    removeFile() {
      this.$emit('input', [])
      this.$emit('file-remove')
      this.currentFile = null
      this.error = ''
    },
    
    getFileUrl(fileId) {
      return `${window.env?.apiPath}/api/public/downloadFile/${fileId}`
    },
    
    formatFileSize(bytes) {
      if (bytes === 0) return '0 B'
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    }
  }
}
</script>

<style scoped>
.mobile-uploader {
  width: 100%;
}

.upload-area {
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #fafafa;
  position: relative;
  min-height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.upload-area:hover {
  border-color: #1890ff;
  background: #f0f8ff;
}

.upload-area.uploading {
  border-color: #1890ff;
  background: #f0f8ff;
  cursor: not-allowed;
  pointer-events: none;
}

.upload-area.has-file {
  border-color: #52c41a;
  background: #f6ffed;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.upload-icon {
  font-size: 32px;
  opacity: 0.6;
}

.upload-text {
  color: #666;
  font-size: 14px;
  margin-bottom: 4px;
}

.upload-hint {
  color: #999;
  font-size: 12px;
}

.file-preview {
  display: flex;
  flex-direction: column;
  width: 100%;
  gap: 12px;
}

/* 图片预览样式 */
.image-preview {
  position: relative;
  width: 100%;
  max-width: 200px;
  margin: 0 auto;
  cursor: pointer;
  border-radius: 8px;
  overflow: hidden;
}

.image-preview img {
  width: 100%;
  height: auto;
  max-height: 150px;
  object-fit: cover;
  display: block;
}

.preview-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.image-preview:hover .preview-overlay {
  opacity: 1;
}

/* 视频预览样式 */
.video-preview {
  width: 100%;
  max-width: 300px;
  margin: 0 auto;
  border-radius: 8px;
  overflow: hidden;
  background: #000;
}

.video-preview video {
  width: 100%;
  height: auto;
  max-height: 200px;
  display: block;
  border-radius: 8px;
}

.video-info {
  padding: 8px 12px;
  background: #f8f9fa;
  text-align: left;
}

.video-info .file-name {
  font-size: 13px;
  color: #333;
  font-weight: 500;
  margin-bottom: 4px;
  word-break: break-all;
}

.video-info .file-size {
  font-size: 12px;
  color: #999;
  margin-bottom: 8px;
}

.play-btn {
  background: #1890ff;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 12px;
  cursor: pointer;
  transition: background 0.3s ease;
}

.play-btn:hover {
  background: #40a9ff;
}

/* 文件信息预览样式 */
.file-info-preview {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 8px;
}

.file-icon {
  font-size: 32px;
  flex-shrink: 0;
}

.file-details {
  flex: 1;
  text-align: left;
}

.file-name {
  font-size: 14px;
  color: #333;
  font-weight: 500;
  margin-bottom: 4px;
  word-break: break-all;
}

.file-size {
  font-size: 12px;
  color: #999;
  margin-bottom: 2px;
}

.file-type {
  font-size: 12px;
  color: #666;
}

.file-actions {
  display: flex;
  gap: 8px;
  justify-content: center;
  margin-top: 8px;
}

.download-btn {
  background: #1890ff;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 12px;
  cursor: pointer;
  transition: background 0.3s ease;
}

.download-btn:hover {
  background: #40a9ff;
}

.remove-btn {
  background: #ff4d4f;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 12px;
  cursor: pointer;
  transition: background 0.3s ease;
}

.remove-btn:hover {
  background: #ff7875;
}

/* 图片预览弹窗样式 */
.image-preview-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  padding: 20px;
}

.modal-content {
  position: relative;
  max-width: 90vw;
  max-height: 90vh;
  background: white;
  border-radius: 8px;
  overflow: hidden;
}

.modal-content img {
  width: 100%;
  height: auto;
  max-width: 100%;
  max-height: 80vh;
  object-fit: contain;
  display: block;
}

.close-btn {
  position: absolute;
  top: 10px;
  right: 10px;
  background: rgba(0, 0, 0, 0.5);
  color: white;
  border: none;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  font-size: 18px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.upload-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 12px;
  background: rgba(255, 255, 255, 0.9);
  color: #1890ff;
  z-index: 10;
}

.loading-text {
  font-size: 14px;
  font-weight: 500;
}

.loading-spinner {
  width: 24px;
  height: 24px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #1890ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-message {
  color: #ff4d4f;
  font-size: 12px;
  margin-top: 8px;
  text-align: center;
}

/* 移动端优化 */
@media (max-width: 768px) {
  .upload-area {
    padding: 16px;
    min-height: 60px;
  }
  
  .upload-icon {
    font-size: 24px;
  }
  
  .upload-text {
    font-size: 13px;
  }
  
  .file-name {
    font-size: 13px;
  }
  
  .remove-btn {
    padding: 6px 10px;
    font-size: 13px;
  }
}
</style>
